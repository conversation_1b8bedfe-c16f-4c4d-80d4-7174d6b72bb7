# Generated by Django 4.2.7 on 2025-06-09 09:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tapemanagement', '0009_fileupload_uploaded_in_request_taperequest_data_size_and_more'),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name='taperequest',
            name='contract_number',
        ),
        migrations.RemoveField(
            model_name='taperequest',
            name='contractor',
        ),
        migrations.AddField(
            model_name='taperequest',
            name='agency',
            field=models.CharField(blank=True, help_text='Agency field', max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='taperequest',
            name='associated_person',
            field=models.Char<PERSON>ield(blank=True, help_text='Person associated with tapes', max_length=30, null=True),
        ),
    ]
