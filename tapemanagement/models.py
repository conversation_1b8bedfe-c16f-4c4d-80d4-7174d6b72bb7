from django.db import models
from django.contrib.auth.models import AbstractUser


class CustomUser(AbstractUser):
    """Custom user model with CPF number for authentication"""
    cpf_number = models.CharField(max_length=14, unique=True, help_text="CPF Number for login")

    USERNAME_FIELD = 'cpf_number'
    REQUIRED_FIELDS = ['username', 'email']

    def __str__(self):
        return f"{self.cpf_number} - {self.username}"


class TapeRequest(models.Model):
    """Model for tape library requests"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]

    GROUP_CHOICES = [
        ('Group 1', 'Group 1'),
        ('Group 2', 'Group 2'),
        ('Group 3', 'Group 3'),
        ('Group 4', 'Group 4'),
        ('TDMS', 'TDMS'),
    ]

    # Data type choices
    DATATYPE_CHOICES = [
        ('2D', '2D'),
        ('3D', '3D'),
    ]

    # Dataset type choices
    DATASET_TYPE_CHOICES = [
        ('<PERSON>rute Stack', 'Brute Stack'),
        ('Brute Stack Velocity', 'Brute Stack Velocity'),
        ('Common offset Cube', 'Common offset Cube'),
        ('Continuous receivers gather', 'Continuous receivers gather'),
        ('Deblended Nav Merged gather', 'Deblended Nav Merged gather'),
        ('Deghost Shot Gather', 'Deghost Shot Gather'),
        ('Deghost Stack', 'Deghost Stack'),
        ('Far Field(FF) signature', 'Far Field(FF) signature'),
        ('Harvested Raw Data', 'Harvested Raw Data'),
        ('Nav Merge Receiver Gather', 'Nav Merge Receiver Gather'),
        ('Nav Merge Shot Gather', 'Nav Merge Shot Gather'),
        ('Navigation Processed(P111)', 'Navigation Processed(P111)'),
        ('Navigation Processed (P2/94)', 'Navigation Processed (P2/94)'),
        ('Navigation Processed(P1/90)', 'Navigation Processed(P1/90)'),
        ('Navigation Raw(P1/90)', 'Navigation Raw(P1/90)'),
        ('Near Offset Cube', 'Near Offset Cube'),
        ('Nearfield Hydrophone Data', 'Nearfield Hydrophone Data'),
        ('NFH-Navmerged', 'NFH-Navmerged'),
        ('Other Data', 'Other Data'),
        ('Picked Velocity', 'Picked Velocity'),
        ('Raw Shot Gather', 'Raw Shot Gather'),
        ('Shot-slice receiver gather', 'Shot-slice receiver gather'),
    ]

    # Data format choices
    DATA_FORMAT_CHOICES = [
        ('ASCII', 'ASCII'),
        ('NA', 'NA'),
        ('P-111', 'P-111'),
        ('P190', 'P190'),
        ('P-294', 'P-294'),
        ('SEG-D', 'SEG-D'),
        ('SEG-D Rev1.0', 'SEG-D Rev1.0'),
        ('SEG-D Rev 2.0', 'SEG-D Rev 2.0'),
        ('SEG-D Rev 2.1', 'SEG-D Rev 2.1'),
        ('SEG-D Rev 3.0', 'SEG-D Rev 3.0'),
        ('SEG-D Rev 3.1', 'SEG-D Rev 3.1'),
        ('SEG-Y', 'SEG-Y'),
        ('SEG-Y Rev 1.0', 'SEG-Y Rev 1.0'),
        ('SEG-Y Rev 2', 'SEG-Y Rev 2'),
        ('SEG-Y Rev 2.0', 'SEG-Y Rev 2.0'),
        ('SEG-Y Rev 2.1', 'SEG-Y Rev 2.1'),
    ]

    # Media type choices (main categories)
    MEDIA_TYPE_CHOICES = [
        ('3592', '3592'),
        ('LTO', 'LTO'),
        ('DISK_STORAGE', 'Disk Storage'),
        ('HDD', 'HDD'),
        ('CD', 'CD'),
    ]

    # User who submitted the request
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='tape_requests')

    # Form fields from the original request.html
    cpf_number = models.CharField(max_length=14)
    username = models.CharField(max_length=100)
    datatype = models.CharField(max_length=10, choices=DATATYPE_CHOICES)
    dataset_type = models.CharField(max_length=100, choices=DATASET_TYPE_CHOICES)
    data_format = models.CharField(max_length=50, choices=DATA_FORMAT_CHOICES)
    date = models.DateField()
    area = models.CharField(max_length=100)
    survey_name = models.CharField(max_length=100)
    project_name = models.CharField(max_length=100)
    acquisition_year = models.CharField(max_length=10, blank=True, null=True)
    group = models.CharField(max_length=20, choices=GROUP_CHOICES)
    activity = models.CharField(max_length=250)

    # New fields
    associated_person = models.CharField(max_length=30, blank=True, null=True, help_text="Person associated with tapes")
    agency = models.CharField(max_length=30, blank=True, null=True, help_text="Agency field")

    # Input Media fields
    input_media_type = models.CharField(max_length=20, choices=MEDIA_TYPE_CHOICES)
    input_media_subtype = models.CharField(max_length=20, blank=True, null=True)
    input_vendor_id = models.CharField(max_length=30)

    # Output Media fields
    output_media_type = models.CharField(max_length=20, choices=MEDIA_TYPE_CHOICES)
    output_media_subtype = models.CharField(max_length=20, blank=True, null=True)

    # Vendor ID fields (required)
    input_vendor_id = models.CharField(max_length=30)
    output_vendor_id = models.CharField(max_length=30)

    remarks = models.TextField(blank=True, null=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    data_size = models.CharField(max_length=100, blank=True, null=True, help_text="Data size (admin only)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def get_input_media_display(self):
        """Get full display name for input media"""
        if self.input_media_subtype:
            return f"{self.input_media_type} - {self.input_media_subtype}"
        return self.input_media_type

    def get_output_media_display(self):
        """Get full display name for output media"""
        if self.output_media_subtype:
            return f"{self.output_media_type} - {self.output_media_subtype}"
        return self.output_media_type

    def __str__(self):
        return f"Request {self.id} - {self.survey_name} by {self.username}"


class FileUpload(models.Model):
    """Model for file uploads associated with tape requests"""
    tape_request = models.ForeignKey(TapeRequest, on_delete=models.CASCADE, related_name='uploads')
    file = models.FileField(upload_to='uploads/%Y/%m/%d/')  # Allow any file type
    filename = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    uploaded_in_request = models.BooleanField(default=False, help_text="True if uploaded during request creation")

    def __str__(self):
        return f"File: {self.filename} for Request {self.tape_request.id}"
