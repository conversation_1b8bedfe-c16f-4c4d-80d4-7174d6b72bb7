from django import forms
from .models import TapeRequest, FileUpload


class <PERSON>pe<PERSON><PERSON><PERSON>Form(forms.ModelForm):
    """Form for creating tape requests"""

    # Add file upload field
    file_upload = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '*/*'  # Accept any file type
        }),
        help_text="Upload any supporting file (optional)"
    )

    class Meta:
        model = TapeRequest
        fields = [
            'cpf_number', 'username', 'datatype', 'dataset_type', 'data_format', 'date',
            'area', 'survey_name', 'acquisition_year', 'associated_person', 'agency',
            'group', 'activity', 'input_media_type', 'input_media_subtype',
            'input_vendor_id', 'output_media_type', 'output_media_subtype',
            'output_vendor_id', 'remarks'
        ]
        widgets = {
            'cpf_number': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': True,
                'placeholder': 'CPF Number'
            }),
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': True,
                'placeholder': 'Name'
            }),
            'datatype': forms.Select(attrs={
                'class': 'form-control'
            }),
            'dataset_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'data_format': forms.Select(attrs={
                'class': 'form-control'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'area': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Area'
            }),
            'survey_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Survey Name'
            }),
            'acquisition_year': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Acquisition Year (e.g., 2024) - Optional'
            }),
            'associated_person': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Person associated with tapes (Optional)',
                'maxlength': '30'
            }),
            'agency': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Agency (Optional)',
                'maxlength': '30'
            }),
            'group': forms.Select(attrs={
                'class': 'form-control'
            }),
            'activity': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter activity description',
                'maxlength': '250'
            }),
            'input_media_type': forms.Select(attrs={
                'class': 'form-control',
                'id': 'id_input_media_type'
            }),
            'input_media_subtype': forms.Select(attrs={
                'class': 'form-control',
                'id': 'id_input_media_subtype',
                'style': 'display: none;'
            }),
            'output_media_type': forms.Select(attrs={
                'class': 'form-control',
                'id': 'id_output_media_type'
            }),
            'output_media_subtype': forms.Select(attrs={
                'class': 'form-control',
                'id': 'id_output_media_subtype',
                'style': 'display: none;'
            }),
            'input_vendor_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Input Vendor ID',
                'maxlength': '30'
            }),
            'output_vendor_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Output Vendor ID',
                'maxlength': '30'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Remarks (optional)'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set default date to today for new forms
        if not self.instance.pk:
            from datetime import date
            self.fields['date'].initial = date.today()

        # Set up subtype choices as empty initially
        self.fields['input_media_subtype'].choices = [('', '-- Select Subtype --')]
        self.fields['output_media_subtype'].choices = [('', '-- Select Subtype --')]

        # If this is an edit form with existing data, populate subtypes
        if self.instance and self.instance.pk:
            if self.instance.input_media_type:
                self.fields['input_media_subtype'].choices = self.get_subtype_choices(self.instance.input_media_type)
            if self.instance.output_media_type:
                self.fields['output_media_subtype'].choices = self.get_subtype_choices(self.instance.output_media_type)

    def get_subtype_choices(self, media_type):
        """Get subtype choices based on media type"""
        choices = [('', '-- Select Subtype --')]

        if media_type == '3592':
            choices.extend([
                ('JA', 'JA'),
                ('JB', 'JB'),
                ('JC', 'JC'),
                ('JD', 'JD'),
                ('JE', 'JE'),
            ])
        elif media_type == 'LTO':
            choices.extend([
                ('1', '1'),
                ('2', '2'),
                ('3', '3'),
                ('4', '4'),
                ('5', '5'),
                ('6', '6'),
                ('7', '7'),
                ('8', '8'),
                ('9', '9'),
            ])
        elif media_type == 'DISK_STORAGE':
            choices.extend([
                ('SPIC_LFS', 'SPIC LFS'),
                ('SPIC_GPFS', 'SPIC GPFS'),
            ])
        elif media_type == 'HDD':
            choices.extend([
                ('5TB', '5TB'),
                ('8TB', '8TB'),
            ])

        return choices


class FileUploadForm(forms.ModelForm):
    """Form for uploading files to tape requests"""

    class Meta:
        model = FileUpload
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '*/*'  # Accept any file type
            })
        }

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # Check file size (limit to 50MB for any file type)
            if file.size > 50 * 1024 * 1024:
                raise forms.ValidationError('File size cannot exceed 50MB.')

        return file


class StatusUpdateForm(forms.ModelForm):
    """Form for admin to update request status"""

    class Meta:
        model = TapeRequest
        fields = ['status']
        widgets = {
            'status': forms.Select(attrs={
                'class': 'form-control'
            })
        }


class AdminDataSizeForm(forms.ModelForm):
    """Form for admin to add/update data size"""

    class Meta:
        model = TapeRequest
        fields = ['data_size']
        widgets = {
            'data_size': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter data size (e.g., 500GB, 2TB)'
            })
        }


class AdminEditRequestForm(forms.ModelForm):
    """Form for admin to edit any request details"""

    class Meta:
        model = TapeRequest
        fields = [
            'cpf_number', 'username', 'datatype', 'dataset_type', 'data_format', 'date',
            'area', 'survey_name', 'acquisition_year', 'associated_person', 'agency',
            'group', 'activity', 'input_media_type', 'input_media_subtype',
            'input_vendor_id', 'output_media_type', 'output_media_subtype',
            'output_vendor_id', 'remarks', 'status', 'data_size'
        ]
        widgets = {
            'cpf_number': forms.TextInput(attrs={'class': 'form-control'}),
            'username': forms.TextInput(attrs={'class': 'form-control'}),
            'datatype': forms.Select(attrs={'class': 'form-control'}),
            'dataset_type': forms.Select(attrs={'class': 'form-control'}),
            'data_format': forms.Select(attrs={'class': 'form-control'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'area': forms.TextInput(attrs={'class': 'form-control'}),
            'survey_name': forms.TextInput(attrs={'class': 'form-control'}),
            'acquisition_year': forms.TextInput(attrs={'class': 'form-control'}),
            'associated_person': forms.TextInput(attrs={'class': 'form-control'}),
            'agency': forms.TextInput(attrs={'class': 'form-control'}),
            'group': forms.Select(attrs={'class': 'form-control'}),
            'activity': forms.TextInput(attrs={'class': 'form-control'}),
            'input_media_type': forms.Select(attrs={'class': 'form-control'}),
            'input_media_subtype': forms.Select(attrs={'class': 'form-control'}),
            'input_vendor_id': forms.TextInput(attrs={'class': 'form-control'}),
            'output_media_type': forms.Select(attrs={'class': 'form-control'}),
            'output_media_subtype': forms.Select(attrs={'class': 'form-control'}),
            'output_vendor_id': forms.TextInput(attrs={'class': 'form-control'}),
            'remarks': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'data_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 500GB, 2TB'})
        }
