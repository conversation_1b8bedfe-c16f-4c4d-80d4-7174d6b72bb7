{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SPIC Tape Data Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --ongc-red: #C41E3A;
            --ongc-orange: #FF6B35;
            --ongc-blue: #003366;
            --ongc-light-blue: #0066CC;
            --ongc-gray: #F5F5F5;
            --ongc-dark-gray: #333333;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--ongc-gray) 0%, #E8E8E8 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* ONGC Header */
        .ongc-header {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, var(--ongc-light-blue) 100%);
            color: white;
            padding: 15px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .ongc-logo {
            max-height: 80px;
            width: auto;
        }

        .ongc-title {
            color: white;
            font-size: 32px;
            font-weight: bold;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .ongc-subtitle {
            color: var(--ongc-orange);
            font-size: 16px;
            margin: 0;
            font-weight: 500;
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--ongc-red) !important;
        }

        .navbar-dark {
            background: linear-gradient(135deg, var(--ongc-dark-gray) 0%, #2C2C2C 100%) !important;
        }

        .header-title {
            color: var(--ongc-red);
            font-family: 'Trebuchet MS', sans-serif;
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            padding: 20px;
            background: linear-gradient(135deg, white 0%, #FAFAFA 100%);
            margin-bottom: 20px;
            border-bottom: 3px solid var(--ongc-orange);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .main-content {
            flex: 1;
            padding-bottom: 60px;
        }

        .card {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 8px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--ongc-light-blue) 0%, var(--ongc-blue) 100%);
            border: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, #002244 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--ongc-orange) 0%, #E55A2B 100%);
            border: none;
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border: none;
        }

        .footer {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, var(--ongc-dark-gray) 100%);
            color: white;
            padding: 30px 0;
            margin-top: auto;
            box-shadow: 0 -4px 8px rgba(0,0,0,0.1);
        }
        
        .footer a {
            color: var(--ongc-orange);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
            text-decoration: underline;
        }

        .alert {
            margin-bottom: 20px;
            border-radius: 8px;
            border: none;
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table th {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, var(--ongc-light-blue) 100%);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }

        .table td {
            vertical-align: middle;
            border-color: #E8E8E8;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 102, 204, 0.05);
        }

        .status-pending {
            color: var(--ongc-orange);
            font-weight: 600;
        }

        .status-approved {
            color: #28a745;
            font-weight: 600;
        }

        .status-rejected {
            color: var(--ongc-red);
            font-weight: 600;
        }

        .status-completed {
            color: #17a2b8;
            font-weight: 600;
        }

        .badge {
            font-size: 11px;
            padding: 6px 10px;
            border-radius: 20px;
        }

        .nav-link {
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            transform: translateY(-1px);
        }

        /* Complete modal fluctuation fix */
        html {
            overflow-y: scroll !important;
        }

        body {
            overflow-x: hidden;
        }

        .modal {
            padding-right: 0 !important;
        }

        .modal-open {
            padding-right: 0 !important;
            margin-right: 0 !important;
        }

        .modal-dialog {
            margin: 1.75rem auto;
            max-width: 800px;
            width: 95%;
        }

        .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, var(--ongc-light-blue) 100%);
            color: white;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
        }

        .modal-title {
            font-weight: 600;
        }

        .btn-close {
            filter: invert(1);
        }

        .modal-body {
            padding: 1.5rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            border-top: 1px solid #E8E8E8;
            padding: 1rem 1.5rem;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- ONGC Header -->
    <header class="ongc-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <img src="{% static 'images/ongc_logo.png' %}" alt="ONGC Logo" class="ongc-logo" onerror="this.style.display='none'">
                </div>
                <div class="col-md-8 text-center">
                    <h1 class="ongc-title">
                        <i class="fas fa-database"></i> SPIC TDMS
                    </h1>
                    <p class="ongc-subtitle">SeisData Processing and Interpretation Centre - Tape Data Management System</p>
                </div>
                <div class="col-md-2">
                    <!-- Empty column for balance -->
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    {% if user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-database"></i> SPIC TDMS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'request_form' %}">
                            <i class="fas fa-plus-circle"></i> New Request
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pending_requests' %}">
                            <i class="fas fa-clock"></i> Pending
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'completed_requests' %}">
                            <i class="fas fa-check-circle"></i> Completed
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                            {% if user.is_superuser %}
                                <span class="badge bg-warning">Admin</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu">
                            {% if user.is_superuser %}
                            <li><a class="dropdown-item" href="/admin/"><i class="fas fa-cog"></i> Admin Panel</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid mt-4" style="max-width: 95%;">
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h6 class="mb-1">
                        <i class="fas fa-database"></i> SPIC Tape Data Management System
                    </h6>
                    <p class="mb-0 small">SeisData Processing and Interpretation Centre</p>
                    <p class="mb-0 small">NBP Green Height, Mumbai - ONGC</p>
                    <p class="mb-2 small">&copy; 2024 ONGC. All rights reserved.</p>
                </div>
            </div>
            <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
            <div class="row">
                <div class="col-12 text-center">
                    <small class="text-muted">
                        Developed for secure and efficient tape management using TS3500, TS4500 and LTO drives
                    </small>
                    <br>
                    <small class="text-muted">
                        <a href="#" class="text-decoration-none">
                            <i class="fas fa-phone"></i> Support
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Simple and effective modal fluctuation fix -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Force scrollbar to always be visible
            document.documentElement.style.overflowY = 'scroll';

            // Override Bootstrap modal behavior
            const modals = document.querySelectorAll('.modal');
            modals.forEach(function(modal) {
                modal.addEventListener('show.bs.modal', function() {
                    document.body.style.paddingRight = '0px';
                    document.body.style.marginRight = '0px';
                });

                modal.addEventListener('shown.bs.modal', function() {
                    document.body.style.paddingRight = '0px';
                    document.body.style.marginRight = '0px';
                });

                modal.addEventListener('hidden.bs.modal', function() {
                    document.body.style.paddingRight = '';
                    document.body.style.marginRight = '';
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
