{% extends 'tapemanagement/base.html' %}

{% block title %}New Request - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    /* Ensure footer stays at bottom */
    body {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .main-content {
        flex: 1;
    }

    .card {
        border: 1px solid #dee2e6;
        box-shadow: none;
    }

    .card-header {
        background-color: #800000;
        color: white;
        border-bottom: 1px solid #dee2e6;
    }

    .btn-primary {
        background-color: #800000;
        border-color: #800000;
    }

    .btn-primary:hover {
        background-color: #600000;
        border-color: #600000;
    }

    .btn-primary:focus, .btn-primary:active {
        background-color: #600000;
        border-color: #600000;
        box-shadow: 0 0 0 0.2rem rgba(128, 0, 0, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-plus-circle"></i> Submit New Tape Request</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.cpf_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-id-card"></i> CPF Number
                                </label>
                                {{ form.cpf_number }}
                                {% if form.cpf_number.errors %}
                                    <div class="text-danger">{{ form.cpf_number.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> Name
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger">{{ form.username.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.datatype.id_for_label }}" class="form-label">
                                    <i class="fas fa-database"></i> Data Type *
                                </label>
                                {{ form.datatype }}
                                {% if form.datatype.errors %}
                                    <div class="text-danger">{{ form.datatype.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.dataset_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-layer-group"></i> Dataset Type *
                                </label>
                                {{ form.dataset_type }}
                                {% if form.dataset_type.errors %}
                                    <div class="text-danger">{{ form.dataset_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.data_format.id_for_label }}" class="form-label">
                                    <i class="fas fa-file-code"></i> Data Format *
                                </label>
                                {{ form.data_format }}
                                {% if form.data_format.errors %}
                                    <div class="text-danger">{{ form.data_format.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar"></i> Date *
                                </label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger">{{ form.date.errors }}</div>
                                {% endif %}
                            </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.area.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Area *
                                </label>
                                {{ form.area }}
                                {% if form.area.errors %}
                                    <div class="text-danger">{{ form.area.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.survey_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-search"></i> Survey Name *
                                </label>
                                {{ form.survey_name }}
                                {% if form.survey_name.errors %}
                                    <div class="text-danger">{{ form.survey_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    

                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.acquisition_year.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Acquisition Year
                                </label>
                                {{ form.acquisition_year }}
                                {% if form.acquisition_year.errors %}
                                    <div class="text-danger">{{ form.acquisition_year.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.group.id_for_label }}" class="form-label">
                                    <i class="fas fa-users"></i> Group *
                                </label>
                                {{ form.group }}
                                {% if form.group.errors %}
                                    <div class="text-danger">{{ form.group.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.activity.id_for_label }}" class="form-label">
                                    <i class="fas fa-tasks"></i> Activity *
                                </label>
                                {{ form.activity }}
                                {% if form.activity.errors %}
                                    <div class="text-danger">{{ form.activity.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- New fields: Associated Person and Agency -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.associated_person.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tie"></i> Associated Person
                                </label>
                                {{ form.associated_person }}
                                {% if form.associated_person.errors %}
                                    <div class="text-danger">{{ form.associated_person.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.agency.id_for_label }}" class="form-label">
                                    <i class="fas fa-building"></i> Agency
                                </label>
                                {{ form.agency }}
                                {% if form.agency.errors %}
                                    <div class="text-danger">{{ form.agency.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.input_media_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-hdd"></i> Input Media *
                                </label>
                                {{ form.input_media_type }}
                                {% if form.input_media_type.errors %}
                                    <div class="text-danger">{{ form.input_media_type.errors }}</div>
                                {% endif %}

                                <!-- Input Media Subtype - appears below main dropdown -->
                                <div id="input_subtype_container" style="display: none; margin-top: 10px;">
                                    <select name="input_media_subtype" class="form-control" id="id_input_media_subtype">
                                        <option value="">-- Select Option --</option>
                                    </select>
                                    {% if form.input_media_subtype.errors %}
                                        <div class="text-danger">{{ form.input_media_subtype.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.input_vendor_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-barcode"></i> Input Vendor ID *
                                </label>
                                {{ form.input_vendor_id }}
                                {% if form.input_vendor_id.errors %}
                                    <div class="text-danger">{{ form.input_vendor_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.output_media_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-save"></i> Output Media *
                                </label>
                                {{ form.output_media_type }}
                                {% if form.output_media_type.errors %}
                                    <div class="text-danger">{{ form.output_media_type.errors }}</div>
                                {% endif %}

                                <!-- Output Media Subtype - appears below main dropdown -->
                                <div id="output_subtype_container" style="display: none; margin-top: 10px;">
                                    <select name="output_media_subtype" class="form-control" id="id_output_media_subtype">
                                        <option value="">-- Select Option --</option>
                                    </select>
                                    {% if form.output_media_subtype.errors %}
                                        <div class="text-danger">{{ form.output_media_subtype.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.output_vendor_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-barcode"></i> Output Vendor ID *
                                </label>
                                {{ form.output_vendor_id }}
                                {% if form.output_vendor_id.errors %}
                                    <div class="text-danger">{{ form.output_vendor_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.remarks.id_for_label }}" class="form-label">
                            <i class="fas fa-comment"></i> Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="text-danger">{{ form.remarks.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- File Upload Section -->
                    <div class="mb-3">
                        <label for="{{ form.file_upload.id_for_label }}" class="form-label">
                            <i class="fas fa-file-upload"></i> Upload Supporting File (Optional)
                        </label>
                        {{ form.file_upload }}
                        {% if form.file_upload.errors %}
                            <div class="text-danger">{{ form.file_upload.errors }}</div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-info-circle text-info"></i>
                            You can upload any type of supporting file. Maximum file size: 50MB.
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Submit Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> Important Notes:</h6>
            <ul class="mb-0">
                <li>All fields marked with (*) are required</li>
                <li>You can upload a supporting file during request creation or later in the Pending section</li>
                <li>Any file type is accepted (maximum 50MB)</li>
                <li>Request status will be updated by administrators</li>
                <li>You will receive notifications about status changes</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Define subtype options for each media type
    const subtypeOptions = {
        '3592': [
            {value: 'JA', text: 'JA'},
            {value: 'JB', text: 'JB'},
            {value: 'JC', text: 'JC'},
            {value: 'JD', text: 'JD'},
            {value: 'JE', text: 'JE'}
        ],
        'LTO': [
            {value: '1', text: '1'},
            {value: '2', text: '2'},
            {value: '3', text: '3'},
            {value: '4', text: '4'},
            {value: '5', text: '5'},
            {value: '6', text: '6'},
            {value: '7', text: '7'},
            {value: '8', text: '8'},
            {value: '9', text: '9'}
        ],
        'DISK_STORAGE': [
            {value: 'SPIC_LFS', text: 'SPIC LFS'},
            {value: 'SPIC_GPFS', text: 'SPIC GPFS'}
        ],
        'HDD': [
            {value: '5TB', text: '5TB'},
            {value: '8TB', text: '8TB'}
        ]
    };

    // Function to update subtype dropdown
    function updateSubtypeDropdown(mediaTypeSelect, subtypeSelect, subtypeContainer) {
        const selectedType = mediaTypeSelect.value;
        console.log('Selected media type:', selectedType); // Debug log

        // Clear existing options
        subtypeSelect.innerHTML = '<option value="">-- Select Option --</option>';

        if (selectedType && subtypeOptions[selectedType]) {
            // Show subtype container
            subtypeContainer.style.display = 'block';
            console.log('Showing subtype container for:', selectedType); // Debug log

            // Add options for selected type
            subtypeOptions[selectedType].forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                subtypeSelect.appendChild(optionElement);
            });
        } else if (selectedType === 'CD') {
            // Hide subtype container for CD only (HDD now has subtypes)
            subtypeContainer.style.display = 'none';
            subtypeSelect.value = '';
        } else {
            // Hide subtype container if no type selected
            subtypeContainer.style.display = 'none';
        }
    }

    // Get form elements
    const inputMediaType = document.getElementById('id_input_media_type');
    const inputMediaSubtype = document.getElementById('id_input_media_subtype');
    const inputSubtypeContainer = document.getElementById('input_subtype_container');

    const outputMediaType = document.getElementById('id_output_media_type');
    const outputMediaSubtype = document.getElementById('id_output_media_subtype');
    const outputSubtypeContainer = document.getElementById('output_subtype_container');

    // Add event listeners for input media
    if (inputMediaType) {
        inputMediaType.addEventListener('change', function() {
            updateSubtypeDropdown(inputMediaType, inputMediaSubtype, inputSubtypeContainer);
        });

        // Initialize on page load if there's already a value
        if (inputMediaType.value) {
            updateSubtypeDropdown(inputMediaType, inputMediaSubtype, inputSubtypeContainer);
        }
    }

    // Add event listeners for output media
    if (outputMediaType) {
        outputMediaType.addEventListener('change', function() {
            updateSubtypeDropdown(outputMediaType, outputMediaSubtype, outputSubtypeContainer);
        });

        // Initialize on page load if there's already a value
        if (outputMediaType.value) {
            updateSubtypeDropdown(outputMediaType, outputMediaSubtype, outputSubtypeContainer);
        }
    }

    // File upload validation
    const fileInput = document.getElementById('id_file_upload');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Check file size (50MB = 50 * 1024 * 1024 bytes)
                if (file.size > 50 * 1024 * 1024) {
                    alert('File size exceeds 50MB limit. Please choose a smaller file.');
                    e.target.value = '';
                    return;
                }
            }
        });
    }
});
</script>
{% endblock %}
