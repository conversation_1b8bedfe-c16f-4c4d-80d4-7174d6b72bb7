{% extends 'tapemanagement/base.html' %}

{% block title %}Pending Requests - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    .file-list {
        margin-bottom: 5px;
    }

    .file-item {
        margin-bottom: 3px;
    }

    .file-link {
        color: #007bff;
        text-decoration: none;
        font-size: 0.85rem;
        display: inline-block;
        padding: 2px 5px;
        border-radius: 3px;
        transition: background-color 0.2s;
    }

    .file-link:hover {
        background-color: #f8f9fa;
        text-decoration: none;
        color: #0056b3;
    }

    .data-size-info {
        background-color: #e8f5e8;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #28a745;
    }
    /* Simple Table Styling */
    .simple-requests-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
        width: 100%;
        min-width: 1200px; /* Ensure minimum width for readability */
    }

    .table-title {
        background: #800000;
        color: white;
        padding: 15px 20px;
        margin: 0;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .simple-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .simple-table th {
        background: #f8f9fa;
        color: #495057;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #007bff;
        font-size: 0.9rem;
    }

    .simple-table td {
        padding: 12px 8px;
        border-bottom: 1px solid #e9ecef;
        vertical-align: top;
        font-size: 0.85rem;
        line-height: 1.4;
        word-wrap: break-word;
    }

    .simple-table tr:hover {
        background-color: #f8f9fa;
    }

    /* Request ID styling */
    .request-id {
        font-weight: bold;
        color: #000000;
        font-size: 1.1rem;
    }

    /* Info rows for organized display */
    .info-item {
        margin-bottom: 5px;
        line-height: 1.4;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        display: inline-block;
        min-width: 70px;
    }

    .info-value {
        color: #6c757d;
    }

    /* Status badges */
    .status-pending { background: #ffc107; color: #000; }
    .status-approved { background: #28a745; color: #fff; }
    .status-rejected { background: #dc3545; color: #fff; }
    .status-completed { background: #17a2b8; color: #fff; }

    .status-badge {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
        display: inline-block;
    }

    /* Simple table styling - no boxes needed */

    /* File badge */
    .file-count {
        background: #ff9800;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Action buttons */
    .btn-simple {
        padding: 5px 12px;
        font-size: 0.8rem;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        margin: 2px;
    }

    .btn-upload {
        background: #f8f9fa;
        color: #495057;
        border: 1px solid #dee2e6;
    }

    .btn-upload:hover {
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .simple-table {
            font-size: 0.8rem;
        }

        .simple-table th,
        .simple-table td {
            padding: 8px;
        }

        .info-label {
            min-width: 50px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-clock"></i> Pending Requests</h2>
    <a href="{% url 'request_form' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> New Request
    </a>
</div>

<!-- Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text"
                       class="form-control"
                       name="search"
                       placeholder="Search by Survey, Area, User, CPF, Group, Person, or Agency..."
                       value="{{ search_query|default:'' }}">
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
            {% if search_query %}
            <div class="col-md-3">
                <a href="{% url 'pending_requests' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear Search
                </a>
            </div>
            {% endif %}
        </form>

        <!-- Search Info -->
        {% if search_query %}
        <div class="mt-3">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Showing pending requests for: "<strong>{{ search_query }}</strong>"
            </small>
        </div>
        {% endif %}
    </div>
</div>

<!-- Simple Organized Table -->
{% if page_obj %}
<div class="simple-requests-table">
    <!-- Table Title -->
    <h4 class="table-title">
        <i class="fas fa-clock"></i> Pending Requests ({{ page_obj.paginator.count }} total)
    </h4>

    <!-- Table -->
    <div class="table-responsive">
        <table class="simple-table">
            <thead>
                <tr>
                    <th style="width: 6%;">ID</th>
                    <th style="width: 18%;">Survey</th>
                    <th style="width: 12%;">Area</th>
                    <th style="width: 15%;">User</th>
                    <th style="width: 8%;">Group</th>
                    <th style="width: 15%;">Input Media</th>
                    <th style="width: 15%;">Output Media</th>
                    <th style="width: 6%;">Status</th>
                    <th style="width: 8%;">Files</th>
                    <th style="width: 7%;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for request in page_obj %}
                <tr>
                    <!-- Request ID Column -->
                    <td>
                        <strong>#{{ request.id }}</strong>
                        <br><small>{{ request.created_at|date:"M d, Y" }}</small>
                    </td>

                    <!-- Survey Column -->
                    <td>
                        <strong>{{ request.survey_name }}</strong>
                        <br><small>{{ request.get_datatype_display }} Data</small>
                        <br><small>Date: {{ request.date|date:"M d, Y" }}</small>
                    </td>

                    <!-- Area Column -->
                    <td>{{ request.area }}</td>

                    <!-- User Column -->
                    <td>
                        <strong>{{ request.username }}</strong>
                        <br><small>CPF: {{ request.cpf_number }}</small>
                        {% if request.associated_person %}
                        <br><small>Person: {{ request.associated_person }}</small>
                        {% endif %}
                        {% if request.agency %}
                        <br><small>Agency: {{ request.agency }}</small>
                        {% endif %}
                    </td>

                    <!-- Group Column -->
                    <td>{{ request.group }}</td>

                    <!-- Input Media Column -->
                    <td>
                        <strong>{{ request.get_input_media_display }}</strong>
                        {% if request.input_vendor_id %}
                        <br><small>Vendor ID: {{ request.input_vendor_id }}</small>
                        {% endif %}
                    </td>

                    <!-- Output Media Column -->
                    <td>
                        <strong>{{ request.get_output_media_display }}</strong>
                        {% if request.output_vendor_id %}
                        <br><small>Vendor ID: {{ request.output_vendor_id }}</small>
                        {% endif %}
                    </td>

                    <!-- Status Column -->
                    <td style="text-align: center;">
                        <span class="status-badge status-{{ request.status }}">
                            {{ request.get_status_display }}
                        </span>
                        <br><small>{{ request.updated_at|date:"M d" }}</small>
                    </td>

                    <!-- Files Column -->
                    <td>
                        {% if request.uploads.all %}
                            <strong>{{ request.uploads.count }} file{{ request.uploads.count|pluralize }}</strong>
                            {% for file in request.uploads.all %}
                                <br><a href="{% url 'view_file' file.id %}" target="_blank" style="font-size: 0.75rem; color: #007bff;">
                                    <i class="fas fa-file"></i> {{ file.filename|truncatechars:20 }}
                                </a>
                            {% endfor %}
                        {% else %}
                            <small style="color: #9ca3af;">No files</small>
                        {% endif %}
                        {% if request.data_size %}
                            <br><br><div style="background: #e8f5e8; padding: 4px 6px; border-radius: 4px; border: 1px solid #28a745;">
                                <strong style="color: #28a745; font-size: 0.8rem;">{{ request.data_size }}</strong>
                            </div>
                        {% elif user.is_superuser %}
                            <br><button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#dataSizeModal{{ request.id }}" style="font-size: 0.7rem;">
                                <i class="fas fa-plus"></i> Add Size
                            </button>
                        {% endif %}
                    </td>

                    <!-- Actions Column -->
                    <td style="text-align: center;">
                        {% if request.status == 'pending' or request.status == 'approved' %}
                        <a href="{% url 'upload_file' request.id %}" class="btn btn-sm btn-outline-primary" style="font-size: 0.7rem;">
                            <i class="fas fa-upload"></i> Upload
                        </a>
                        {% endif %}

                        {% if user.is_superuser and request.status == 'pending' %}
                        <br>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" style="font-size: 0.7rem;">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="approved">
                                        <button type="submit" class="dropdown-item text-success">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="rejected">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-times"></i> Reject
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="completed">
                                        <button type="submit" class="dropdown-item text-info">
                                            <i class="fas fa-check-circle"></i> Complete
                                        </button>
                                    </form>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a href="{% url 'admin_edit_request' request.id %}" class="dropdown-item">
                                        <i class="fas fa-edit"></i> Edit Details
                                    </a>
                                </li>
                            </ul>
                        </div>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
        <h5>No requests found</h5>
        <p class="text-muted">You haven't submitted any requests yet.</p>
        <a href="{% url 'request_form' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Submit Your First Request
        </a>
    </div>
</div>
{% endif %}

<!-- Data Size Modals for Admin -->
{% if user.is_superuser %}
    {% for request in page_obj %}
        {% if not request.data_size %}
        <div class="modal fade" id="dataSizeModal{{ request.id }}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Data Size - Request #{{ request.id }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="post" action="{% url 'admin_update_data_size' request.id %}">
                        <div class="modal-body">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="data_size{{ request.id }}" class="form-label">Data Size</label>
                                <input type="text" class="form-control" id="data_size{{ request.id }}" name="data_size"
                                       placeholder="e.g., 500GB, 2TB, 1.5TB" required>
                                <div class="form-text">Enter the data size for this request</div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Save Data Size</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        {% endif %}
    {% endfor %}
{% endif %}
{% endblock %}
