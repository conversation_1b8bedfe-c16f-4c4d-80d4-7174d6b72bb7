{% extends 'tapemanagement/base.html' %}

{% block title %}Edit Request #{{ tape_request.id }} - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: 1px solid #dee2e6;
        box-shadow: none;
    }

    .card-header {
        background-color: #800000;
        color: white;
        border-bottom: 1px solid #dee2e6;
    }

    .section-header {
        color: #800000;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .form-section {
        margin-bottom: 25px;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .admin-controls {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }

    .required-field {
        color: #dc3545;
    }

    .btn-primary {
        background-color: #800000;
        border-color: #800000;
    }

    .btn-primary:hover {
        background-color: #600000;
        border-color: #600000;
    }

    .alert-info {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
    }

    .card-header .text-muted {
        color: white !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-edit"></i> Edit Request #{{ tape_request.id }}</h4>
                <small class="text-muted">Submitted by: {{ tape_request.username }} ({{ tape_request.cpf_number }})</small>
            </div>
            <div class="card-body">
                <!-- Request Summary -->
                <div class="alert alert-info mb-4">
                    <h6><i class="fas fa-info-circle"></i> Request Summary</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Request ID:</strong> #{{ tape_request.id }}
                        </div>
                        <div class="col-md-3">
                            <strong>Status:</strong>
                            <span class="badge bg-{{ tape_request.status|default:'secondary' }}">{{ tape_request.get_status_display }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Created:</strong> {{ tape_request.created_at|date:"M d, Y H:i" }}
                        </div>
                        <div class="col-md-3">
                            <strong>Updated:</strong> {{ tape_request.updated_at|date:"M d, Y H:i" }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>Survey:</strong> {{ tape_request.survey_name }}
                        </div>
                        <div class="col-md-6">
                            <strong>Files:</strong> {{ tape_request.uploads.count }} uploaded
                        </div>
                    </div>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <!-- User Information -->
                    <div class="form-section">
                        <h5 class="section-header"><i class="fas fa-user"></i> User Information</h5>
                        <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.cpf_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-id-card"></i> CPF Number <span class="required-field">*</span>
                                </label>
                                {{ form.cpf_number }}
                                {% if form.cpf_number.errors %}
                                    <div class="text-danger">{{ form.cpf_number.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> Username *
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger">{{ form.username.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Data Type and Format -->
                    <div class="form-section">
                        <h5 class="section-header"><i class="fas fa-database"></i> Data Information</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.datatype.id_for_label }}" class="form-label">
                                    <i class="fas fa-database"></i> Data Type *
                                </label>
                                {{ form.datatype }}
                                {% if form.datatype.errors %}
                                    <div class="text-danger">{{ form.datatype.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.dataset_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-layer-group"></i> Dataset Type *
                                </label>
                                {{ form.dataset_type }}
                                {% if form.dataset_type.errors %}
                                    <div class="text-danger">{{ form.dataset_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.data_format.id_for_label }}" class="form-label">
                                    <i class="fas fa-file-code"></i> Data Format *
                                </label>
                                {{ form.data_format }}
                                {% if form.data_format.errors %}
                                    <div class="text-danger">{{ form.data_format.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Project Information -->
                    <div class="form-section">
                        <h5 class="section-header"><i class="fas fa-project-diagram"></i> Project Information</h5>

                        <!-- Date and Area -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar"></i> Date *
                                </label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger">{{ form.date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.area.id_for_label }}" class="form-label">
                                    <i class="fas fa-map"></i> Area *
                                </label>
                                {{ form.area }}
                                {% if form.area.errors %}
                                    <div class="text-danger">{{ form.area.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Survey Name -->
                    <div class="mb-3">
                        <label for="{{ form.survey_name.id_for_label }}" class="form-label">
                            <i class="fas fa-search"></i> Survey Name *
                        </label>
                        {{ form.survey_name }}
                        {% if form.survey_name.errors %}
                            <div class="text-danger">{{ form.survey_name.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- Acquisition Year and Group -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.acquisition_year.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Acquisition Year
                                </label>
                                {{ form.acquisition_year }}
                                {% if form.acquisition_year.errors %}
                                    <div class="text-danger">{{ form.acquisition_year.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.group.id_for_label }}" class="form-label">
                                    <i class="fas fa-users"></i> Group *
                                </label>
                                {{ form.group }}
                                {% if form.group.errors %}
                                    <div class="text-danger">{{ form.group.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Associated Person and Agency -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.associated_person.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tie"></i> Associated Person
                                </label>
                                {{ form.associated_person }}
                                {% if form.associated_person.errors %}
                                    <div class="text-danger">{{ form.associated_person.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.agency.id_for_label }}" class="form-label">
                                    <i class="fas fa-building"></i> Agency
                                </label>
                                {{ form.agency }}
                                {% if form.agency.errors %}
                                    <div class="text-danger">{{ form.agency.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Activity -->
                    <div class="mb-3">
                        <label for="{{ form.activity.id_for_label }}" class="form-label">
                            <i class="fas fa-tasks"></i> Activity *
                        </label>
                        {{ form.activity }}
                        {% if form.activity.errors %}
                            <div class="text-danger">{{ form.activity.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- Media Information -->
                    <div class="form-section">
                        <h5 class="section-header"><i class="fas fa-hdd"></i> Media Information</h5>

                        <!-- Input Media Information -->
                    <h6 class="mb-3"><i class="fas fa-arrow-down"></i> Input Media</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.input_media_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-hdd"></i> Input Media Type *
                                </label>
                                {{ form.input_media_type }}
                                {% if form.input_media_type.errors %}
                                    <div class="text-danger">{{ form.input_media_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.input_media_subtype.id_for_label }}" class="form-label">
                                    <i class="fas fa-list"></i> Input Media Subtype
                                </label>
                                {{ form.input_media_subtype }}
                                {% if form.input_media_subtype.errors %}
                                    <div class="text-danger">{{ form.input_media_subtype.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.input_vendor_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-tag"></i> Input Vendor ID *
                                </label>
                                {{ form.input_vendor_id }}
                                {% if form.input_vendor_id.errors %}
                                    <div class="text-danger">{{ form.input_vendor_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Output Media Information -->
                    <h6 class="mt-4 mb-3"><i class="fas fa-arrow-up"></i> Output Media</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.output_media_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-hdd"></i> Output Media Type *
                                </label>
                                {{ form.output_media_type }}
                                {% if form.output_media_type.errors %}
                                    <div class="text-danger">{{ form.output_media_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.output_media_subtype.id_for_label }}" class="form-label">
                                    <i class="fas fa-list"></i> Output Media Subtype
                                </label>
                                {{ form.output_media_subtype }}
                                {% if form.output_media_subtype.errors %}
                                    <div class="text-danger">{{ form.output_media_subtype.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.output_vendor_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-tag"></i> Output Vendor ID *
                                </label>
                                {{ form.output_vendor_id }}
                                {% if form.output_vendor_id.errors %}
                                    <div class="text-danger">{{ form.output_vendor_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="form-section">
                        <h5 class="section-header"><i class="fas fa-info-circle"></i> Additional Information</h5>

                        <!-- Remarks -->
                    <div class="mb-3">
                        <label for="{{ form.remarks.id_for_label }}" class="form-label">
                            <i class="fas fa-comment"></i> Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="text-danger">{{ form.remarks.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- Admin Controls -->
                    <div class="admin-controls">
                        <h5 class="section-header"><i class="fas fa-cog"></i> Admin Controls</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    <i class="fas fa-flag"></i> Status *
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="text-danger">{{ form.status.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.data_size.id_for_label }}" class="form-label">
                                    <i class="fas fa-hdd"></i> Data Size
                                </label>
                                {{ form.data_size }}
                                {% if form.data_size.errors %}
                                    <div class="text-danger">{{ form.data_size.errors }}</div>
                                {% endif %}
                                <div class="form-text">e.g., 500GB, 2TB, 1.5TB</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{% url 'pending_requests' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Define subtype options for each media type
    const subtypeOptions = {
        '3592': [
            {value: 'JA', text: 'JA'},
            {value: 'JB', text: 'JB'},
            {value: 'JC', text: 'JC'},
            {value: 'JD', text: 'JD'},
            {value: 'JE', text: 'JE'}
        ],
        'LTO': [
            {value: '1', text: '1'},
            {value: '2', text: '2'},
            {value: '3', text: '3'},
            {value: '4', text: '4'},
            {value: '5', text: '5'},
            {value: '6', text: '6'},
            {value: '7', text: '7'},
            {value: '8', text: '8'},
            {value: '9', text: '9'}
        ],
        'DISK_STORAGE': [
            {value: 'SPIC_LFS', text: 'SPIC LFS'},
            {value: 'SPIC_GPFS', text: 'SPIC GPFS'}
        ],
        'HDD': [
            {value: '5TB', text: '5TB'},
            {value: '8TB', text: '8TB'}
        ]
    };

    // Function to update subtype dropdown
    function updateSubtypeDropdown(mediaTypeSelect, subtypeSelect) {
        const selectedType = mediaTypeSelect.value;

        // Clear existing options
        subtypeSelect.innerHTML = '<option value="">-- Select Option --</option>';

        if (selectedType && subtypeOptions[selectedType]) {
            // Add options for selected type
            subtypeOptions[selectedType].forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                subtypeSelect.appendChild(optionElement);
            });
        }
    }

    // Get form elements
    const inputMediaType = document.getElementById('id_input_media_type');
    const inputMediaSubtype = document.getElementById('id_input_media_subtype');
    const outputMediaType = document.getElementById('id_output_media_type');
    const outputMediaSubtype = document.getElementById('id_output_media_subtype');

    // Add event listeners for input media
    if (inputMediaType && inputMediaSubtype) {
        inputMediaType.addEventListener('change', function() {
            updateSubtypeDropdown(inputMediaType, inputMediaSubtype);
        });

        // Initialize on page load if there's already a value
        if (inputMediaType.value) {
            updateSubtypeDropdown(inputMediaType, inputMediaSubtype);
        }
    }

    // Add event listeners for output media
    if (outputMediaType && outputMediaSubtype) {
        outputMediaType.addEventListener('change', function() {
            updateSubtypeDropdown(outputMediaType, outputMediaSubtype);
        });

        // Initialize on page load if there's already a value
        if (outputMediaType.value) {
            updateSubtypeDropdown(outputMediaType, outputMediaSubtype);
        }
    }
});
</script>
{% endblock %}
