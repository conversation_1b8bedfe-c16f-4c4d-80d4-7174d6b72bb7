%{
#include <stdio.h>
%}

%%

"if"|"else"|"while"|"return" { printf("Keyword: %s\n", yytext); }

[a-zA-Z_][a-zA-Z0-9_]*       { printf("Identifier: %s\n", yytext); }

[0-9]+                       { printf("Number: %s\n", yytext); }

"=="|"!="|"<="|">="|"="|"+"|"-"|"*"|"/" { printf("Operator: %s\n", yytext); }

[;,\(\)\{\}]                 { printf("Special Character: %s\n", yytext); }

[ \t\n]+                     ;
.                            { printf("Unknown Token: %s\n", yytext); }

%%

int main() {
    printf("Enter input: ");
    fflush(stdout);
    yylex();
    return 0;
}
