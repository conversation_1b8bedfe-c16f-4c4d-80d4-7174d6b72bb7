%{
#include <stdio.h>

int char_count = 0, word_count = 0, line_count = 0, space_count = 0;
%}

%%
[^\t\n ]+ { word_count++; char_count += yyleng;}
[\t ] { space_count++; char_count++;}
\n { line_count++; char_count++;}
. { char_count++;}
%%

int main() {
    printf("Enter text:\n");
    yylex();
    
    printf("\nTotal Characters: %d\n", char_count);
    printf("Total Words: %d\n", word_count);
    printf("Total Spaces: %d\n", space_count);
    printf("Total Lines: %d\n", line_count);
    
    return 0;
}
